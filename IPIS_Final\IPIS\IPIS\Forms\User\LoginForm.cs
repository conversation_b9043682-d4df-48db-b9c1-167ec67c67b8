using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using System.IO;
using IPIS.Utils;
using IPIS.Services;
using IPIS.Repositories;
using IPIS.Models;
using UserModel = IPIS.Models.User;

namespace IPIS.Forms.User
{
    public partial class LoginForm : Form
    {
        private Panel mainPanel;
        private Panel leftPanel;
        private Panel rightPanel;
        private Label titleLabel;
        private Label subtitleLabel;
        private Panel usernamePanel;
        private Panel passwordPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnCancel;
        private Label lblUsername;
        private Label lblPassword;
        private PictureBox logoBox;
        private Label welcomeLabel;
        private Label systemLabel;
        private Panel buttonPanel;
        private Label poweredByLabel;
        private readonly ToastNotification toast;
        private readonly UserService userService;
        private readonly LoginConfigurationService configService;
        private LoginConfiguration loginConfig;

        public LoginForm()
        {
            // Initialize services and configuration BEFORE InitializeComponent
            userService = new UserService(new SQLiteUserRepository());
            configService = new LoginConfigurationService(new SQLiteLoginConfigurationRepository());
            loginConfig = configService.GetLoginConfiguration();

            // Now initialize the component with configuration available
            InitializeComponent();

            // Initialize toast notification AFTER the form is initialized
            toast = new ToastNotification(this);
        }

        private void InitializeComponent()
        {
            // Form properties - Make full screen
            this.WindowState = FormWindowState.Maximized;
            this.FormBorderStyle = FormBorderStyle.None;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = loginConfig.GetBackgroundColor();
            this.Text = $"{loginConfig.StationName} - Login";

            CreateMainLayout();
            CreateLeftPanel();
            CreateRightPanel();
            SetupEventHandlers();
        }

        private void CreateMainLayout()
        {
            // Main container panel
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent
            };
            this.Controls.Add(mainPanel);

            // Left panel for branding - fixed width
            leftPanel = new Panel
            {
                Dock = DockStyle.Left,
                Width = 800, // Fixed width instead of percentage
                BackColor = loginConfig.GetPrimaryColor()
            };

            // Add background image if configured
            if (loginConfig.UseBackgroundImage && !string.IsNullOrEmpty(loginConfig.BackgroundImagePath) && File.Exists(loginConfig.BackgroundImagePath))
            {
                try
                {
                    leftPanel.BackgroundImage = Image.FromFile(loginConfig.BackgroundImagePath);
                    leftPanel.BackgroundImageLayout = ImageLayout.Stretch;
                }
                catch
                {
                    // If image loading fails, keep the solid color background
                }
            }

            mainPanel.Controls.Add(leftPanel);

            // Right panel for login form - takes remaining space
            rightPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White // Keep white background
            };
            mainPanel.Controls.Add(rightPanel);
        }

        private void CreateLeftPanel()
        {
            // Left panel now only contains the configurable background color or image
            // All text elements (logo, station name, subtitle) are moved to the right panel
        }

        private void CreateRightPanel()
        {
            // Create a container panel to center all content
            var containerPanel = new Panel
            {
                Size = new Size(500, 750), // Increased height to prevent text cutoff
                BackColor = Color.White,
                Location = new Point(50, 50) // Initial position, will be centered later
            };

            rightPanel.Controls.Add(containerPanel);

            int yPos = 30; // Start with more top margin

            // Logo/Icon at the top
            logoBox = new PictureBox
            {
                Size = new Size(80, 80),
                Location = new Point((containerPanel.Width - 80) / 2, yPos),
                SizeMode = PictureBoxSizeMode.StretchImage,
                Image = GetLogoImage(),
                BackColor = Color.Transparent
            };
            containerPanel.Controls.Add(logoBox);
            yPos += 100;

            // Welcome label
            welcomeLabel = new Label
            {
                Text = loginConfig.WelcomeMessage,
                Font = new Font("Segoe UI", 16, FontStyle.Regular),
                ForeColor = loginConfig.GetStationTextColor(),
                Size = new Size(containerPanel.Width, 30),
                Location = new Point(0, yPos),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            containerPanel.Controls.Add(welcomeLabel);
            yPos += 30;

            // System title (Station Name)
            systemLabel = new Label
            {
                Text = loginConfig.StationName,
                Font = new Font("Segoe UI", 28, FontStyle.Bold),
                ForeColor = loginConfig.GetStationTextColor(),
                Size = new Size(containerPanel.Width, 45), // Increased height
                Location = new Point(0, yPos),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            containerPanel.Controls.Add(systemLabel);
            yPos += 55;

            // Subtitle
            subtitleLabel = new Label
            {
                Text = loginConfig.SubtitleMessage,
                Font = new Font("Segoe UI", 14, FontStyle.Regular),
                ForeColor = loginConfig.GetStationTextColor(),
                Size = new Size(containerPanel.Width, 30), // Increased height
                Location = new Point(0, yPos),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            containerPanel.Controls.Add(subtitleLabel);
            yPos += 60;

            // Username section
            CreateUsernameSection(containerPanel, ref yPos);

            // Password section
            CreatePasswordSection(containerPanel, ref yPos);

            // Button section
            CreateButtonSection(containerPanel, ref yPos);

            // Close button (X) - positioned in top-right of right panel
            CreateCloseButton();

            // Powered by Digispin footer - positioned at bottom of right panel
            CreatePoweredByFooter();

            // Handle resize and load to keep container centered
            EventHandler centerContainer = (s, e) =>
            {
                if (rightPanel.Width > 0 && rightPanel.Height > 0)
                {
                    containerPanel.Location = new Point(
                        Math.Max(10, (rightPanel.Width - containerPanel.Width) / 2),
                        Math.Max(10, (rightPanel.Height - containerPanel.Height) / 2)
                    );
                }
            };

            rightPanel.Resize += centerContainer;
            this.Load += centerContainer;
            this.Shown += centerContainer;
        }

        private void CreateUsernameSection(Panel container, ref int yPos)
        {
            // Username label
            lblUsername = new Label
            {
                Text = "Username",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point((container.Width - 320) / 2, yPos),
                Size = new Size(100, 25)
            };
            container.Controls.Add(lblUsername);
            yPos += 30;

            // Username panel with border
            usernamePanel = new Panel
            {
                Size = new Size(320, 50),
                Location = new Point((container.Width - 320) / 2, yPos),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            container.Controls.Add(usernamePanel);

            // Username textbox
            txtUsername = new TextBox
            {
                Size = new Size(300, 30),
                Location = new Point(10, 10),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.None,
                BackColor = Color.White,
                TabIndex = 0
            };
            usernamePanel.Controls.Add(txtUsername);
            yPos += 70;
        }

        private void CreatePasswordSection(Panel container, ref int yPos)
        {
            // Password label
            lblPassword = new Label
            {
                Text = "Password",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point((container.Width - 320) / 2, yPos),
                Size = new Size(100, 25)
            };
            container.Controls.Add(lblPassword);
            yPos += 30;

            // Password panel with border
            passwordPanel = new Panel
            {
                Size = new Size(320, 50),
                Location = new Point((container.Width - 320) / 2, yPos),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            container.Controls.Add(passwordPanel);

            // Password textbox
            txtPassword = new TextBox
            {
                Size = new Size(300, 30),
                Location = new Point(10, 10),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.None,
                BackColor = Color.White,
                PasswordChar = '●',
                TabIndex = 1
            };
            passwordPanel.Controls.Add(txtPassword);
            yPos += 70;
        }

        private void CreateButtonSection(Panel container, ref int yPos)
        {
            // Button panel
            buttonPanel = new Panel
            {
                Size = new Size(320, 100),
                Location = new Point((container.Width - 320) / 2, yPos),
                BackColor = Color.Transparent
            };
            container.Controls.Add(buttonPanel);

            // Login button with blue theme
            btnLogin = new Button
            {
                Text = "Sign In",
                Size = new Size(320, 45),
                Location = new Point(0, 0),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                TabIndex = 2,
                FlatStyle = FlatStyle.Flat,
                BackColor = loginConfig.GetPrimaryColor(),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };
            btnLogin.FlatAppearance.BorderSize = 0;

            // Create darker shades for hover and click effects
            Color primaryColor = loginConfig.GetPrimaryColor();
            Color hoverColor = ControlPaint.Dark(primaryColor, 0.1f);
            Color clickColor = ControlPaint.Dark(primaryColor, 0.2f);

            btnLogin.FlatAppearance.MouseOverBackColor = hoverColor;
            btnLogin.FlatAppearance.MouseDownBackColor = clickColor;
            buttonPanel.Controls.Add(btnLogin);

            // Cancel button
            btnCancel = new Button
            {
                Text = "Cancel",
                Size = new Size(320, 40),
                Location = new Point(0, 55),
                Font = new Font("Segoe UI", 11, FontStyle.Regular),
                TabIndex = 3,
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent,
                ForeColor = Color.FromArgb(108, 117, 125),
                Cursor = Cursors.Hand,
                DialogResult = DialogResult.Cancel
            };
            btnCancel.FlatAppearance.BorderSize = 1;
            btnCancel.FlatAppearance.BorderColor = Color.FromArgb(206, 212, 218);
            btnCancel.FlatAppearance.MouseOverBackColor = Color.FromArgb(248, 249, 250);
            buttonPanel.Controls.Add(btnCancel);
            yPos += 120;
        }

        private void CreateCloseButton()
        {
            Button closeButton = new Button
            {
                Text = "×",
                Size = new Size(40, 40),
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent,
                ForeColor = Color.FromArgb(108, 117, 125),
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(248, 249, 250);
            closeButton.Click += (s, e) => this.Close();

            // Position in top-right corner of right panel
            closeButton.Location = new Point(rightPanel.Width - 50, 10);
            rightPanel.Controls.Add(closeButton);

            // Update position when right panel resizes
            rightPanel.Resize += (s, e) =>
            {
                closeButton.Location = new Point(rightPanel.Width - 50, 10);
            };
        }

        private void CreatePoweredByFooter()
        {
            poweredByLabel = new Label
            {
                Text = "Powered by Digispin",
                Font = new Font("Segoe UI", 9, FontStyle.Regular),
                ForeColor = Color.FromArgb(108, 117, 125),
                Size = new Size(200, 20),
                BackColor = Color.Transparent,
                TextAlign = ContentAlignment.MiddleCenter,
                Anchor = AnchorStyles.Bottom
            };

            // Position at bottom center of right panel
            poweredByLabel.Location = new Point(
                (rightPanel.Width - poweredByLabel.Width) / 2,
                rightPanel.Height - 30
            );
            rightPanel.Controls.Add(poweredByLabel);

            // Update position when right panel resizes
            rightPanel.Resize += (s, e) =>
            {
                poweredByLabel.Location = new Point(
                    (rightPanel.Width - poweredByLabel.Width) / 2,
                    rightPanel.Height - 30
                );
            };
        }

        private Image GetLogoImage()
        {
            // Try to load custom logo if configured
            if (loginConfig.UseCustomLogo && !string.IsNullOrEmpty(loginConfig.LogoPath) && File.Exists(loginConfig.LogoPath))
            {
                try
                {
                    return Image.FromFile(loginConfig.LogoPath);
                }
                catch
                {
                    // If custom logo fails to load, fall back to default
                }
            }

            // Create default train/railway icon
            return CreateDefaultLogoImage();
        }

        private Image CreateDefaultLogoImage()
        {
            // Create a simple train/railway icon
            Bitmap logo = new Bitmap(80, 80);
            using (Graphics g = Graphics.FromImage(logo))
            {
                g.SmoothingMode = SmoothingMode.AntiAlias;
                g.Clear(Color.Transparent);

                // Draw a simple train icon
                using (Brush brush = new SolidBrush(Color.White))
                {
                    // Train body
                    g.FillRectangle(brush, 10, 30, 60, 25);
                    // Train front
                    g.FillRectangle(brush, 5, 35, 10, 15);
                    // Wheels
                    g.FillEllipse(brush, 15, 50, 8, 8);
                    g.FillEllipse(brush, 30, 50, 8, 8);
                    g.FillEllipse(brush, 45, 50, 8, 8);
                    g.FillEllipse(brush, 60, 50, 8, 8);
                }
            }
            return logo;
        }

        private void DrawTextBoxBorder(PaintEventArgs e, Panel panel, bool focused)
        {
            Color borderColor = focused ? loginConfig.GetPrimaryColor() : Color.FromArgb(206, 212, 218);
            int borderWidth = focused ? 2 : 1;

            using (Pen pen = new Pen(borderColor, borderWidth))
            {
                Rectangle rect = new Rectangle(0, 0, panel.Width - 1, panel.Height - 1);
                e.Graphics.DrawRectangle(pen, rect);
            }
        }

        private void SetupEventHandlers()
        {
            // Form events
            this.KeyPreview = true;
            this.KeyDown += LoginForm_KeyDown;

            // Button events
            btnLogin.Click += BtnLogin_Click;
            btnCancel.Click += (s, e) => this.Close();

            // Enter key handling
            txtPassword.KeyDown += (s, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    BtnLogin_Click(s, e);
                }
            };

            // Set accept and cancel buttons
            this.AcceptButton = btnLogin;
            this.CancelButton = btnCancel;
        }



        private void LoginForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                this.Close();
            }
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                toast.ShowError("Please enter your username.");
                txtUsername.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                toast.ShowError("Please enter your password.");
                txtPassword.Focus();
                return;
            }

            // Disable login button to prevent multiple clicks
            btnLogin.Enabled = false;
            btnLogin.Text = "Signing In...";

            try
            {
                // Authenticate user
                UserModel authenticatedUser = userService.AuthenticateUser(txtUsername.Text, txtPassword.Text);

                if (authenticatedUser != null)
                {
                    // Set up session
                    SessionManager.Login(authenticatedUser);

                    toast.ShowSuccess($"Welcome, {authenticatedUser.Username}!");
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    toast.ShowError("Invalid username or password.");
                    txtPassword.Clear();
                    txtUsername.Focus();
                }
            }
            catch (Exception ex)
            {
                toast.ShowError($"Login error: {ex.Message}");
            }
            finally
            {
                // Re-enable login button
                btnLogin.Enabled = true;
                btnLogin.Text = "Sign In";
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);

            // Add subtle shadow effect to the form
            using (Pen shadowPen = new Pen(Color.FromArgb(50, 0, 0, 0), 2))
            {
                e.Graphics.DrawRectangle(shadowPen, 2, 2, this.Width - 4, this.Height - 4);
            }
        }
    }
}